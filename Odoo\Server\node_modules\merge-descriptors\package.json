{"name": "merge-descriptors", "version": "2.0.0", "description": "Merge objects using their property descriptors", "license": "MIT", "repository": "sindresorhus/merge-descriptors", "funding": "https://github.com/sponsors/sindresorhus", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> Sorhus <<EMAIL>>"], "exports": {"types": "./index.d.ts", "default": "./index.js"}, "main": "./index.js", "types": "./index.d.ts", "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava"}, "files": ["index.js", "index.d.ts"], "keywords": ["merge", "descriptors", "object", "property", "properties", "merging", "getter", "setter"], "devDependencies": {"ava": "^5.3.1", "xo": "^0.56.0"}, "xo": {"rules": {"unicorn/prefer-module": "off"}}}