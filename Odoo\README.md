# Odoo - Skill Swap Platform

## Project Overview
Develop a Skill Swap Platform — a mini application that enables users to list their skills and request others in return.

## Team Members
- <EMAIL>
- <EMAIL>
- <EMAIL>
- <EMAIL>

## Features

### User Profile Management
- **Basic Info**: Name, location (optional), profile photo (optional)
- **Skills Management**: 
  - List of skills offered
  - List of skills wanted
- **Availability**: Set availability (e.g., weekends, evenings)
- **Privacy Control**: Users can make their profile public or private

### Skill Discovery & Search
- Browse or search other users by skill (e.g., "Photoshop" or "Excel")
- Filter users based on availability and location

### Swap Management
- **Request & Accept Swaps**:
  - Send swap requests to other users
  - Accept or reject incoming swap offers
  - View current and pending swap requests
- **Request Management**: Users can delete swap requests if not accepted
- **Feedback System**: Ratings or feedback after completing a swap

### Admin Panel
- **Content Moderation**: Reject inappropriate or spammy skill descriptions
- **User Management**: Ban users who violate platform policies
- **Swap Monitoring**: Monitor pending, accepted, or cancelled swaps
- **Communication**: Send platform-wide messages (e.g., feature updates, downtime alerts)
- **Analytics**: Download reports of user activity, feedback logs, and swap statistics

## Technology Stack
- Backend: Node.js with Express.js
- Database: (To be determined)
- Frontend: (To be determined)
- Authentication: (To be determined)

## Getting Started
(Instructions to be added as development progresses)

## Project Status
🚧 **In Development** - Project setup and initial planning phase